import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:health_diary/pages/home/<USER>/recent_records_list.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

void main() {
  group('RecordItem Expand Animation Tests', () {
    testWidgets('should expand note with animation when tapped', (WidgetTester tester) async {
      // 创建一个有备注的记录
      final recordWithNote = HealthRecordEntry(
        id: 1,
        time: DateTime.now(),
        note: '这是一个测试备注，用来验证展开动画效果',
        recordType: HealthRecordTypeEnum.bloodPressure,
        values: [
          HealthRecordValue(
            label: '收缩压',
            value: '120',
            unit: 'mmHg',
            isAbnormal: false,
          ),
        ],
        color: 0xFFFFEBF0,
      );

      // 构建测试widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordItem(record: recordWithNote),
          ),
        ),
      );

      // 验证初始状态：备注不显示
      expect(find.text('这是一个测试备注，用来验证展开动画效果'), findsNothing);

      // 点击记录项触发展开动画
      await tester.tap(find.byType(RecordItem));
      
      // 等待动画完成（300ms）
      await tester.pumpAndSettle(const Duration(milliseconds: 300));

      // 验证备注现在显示了
      expect(find.text('这是一个测试备注，用来验证展开动画效果'), findsOneWidget);
      
      // 再次点击收起
      await tester.tap(find.byType(RecordItem));
      await tester.pumpAndSettle(const Duration(milliseconds: 300));
      
      // 验证备注又隐藏了
      expect(find.text('这是一个测试备注，用来验证展开动画效果'), findsNothing);
    });

    testWidgets('should use AnimatedSize for smooth expand animation', (WidgetTester tester) async {
      final recordWithNote = HealthRecordEntry(
        id: 1,
        time: DateTime.now(),
        note: '测试动画',
        recordType: HealthRecordTypeEnum.bloodSugar,
        values: [
          HealthRecordValue(
            label: '血糖',
            value: '5.5',
            unit: 'mmol/L',
            isAbnormal: false,
          ),
        ],
        color: 0xFFE4F4FF,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordItem(record: recordWithNote),
          ),
        ),
      );

      // 验证AnimatedSize组件存在
      expect(find.byType(AnimatedSize), findsOneWidget);
      
      // 获取AnimatedSize组件
      final animatedSize = tester.widget<AnimatedSize>(find.byType(AnimatedSize));
      
      // 验证动画配置
      expect(animatedSize.duration, const Duration(milliseconds: 300));
      expect(animatedSize.curve, Curves.easeInOut);
    });
  });
}
