import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:health_diary/health_service/service_manager.dart';
import 'package:health_diary/providers/health_type_provider.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_types.dart';
import 'package:health_diary/widgets/confirm_delete_dialog.dart';

import '../../themes/app_theme.dart';
import 'controller/add_record_controller.dart';
import 'widgets/input_form.dart';
import 'widgets/scan_widget.dart';
import 'widgets/record_type_tabs.dart';

class AddRecordPage extends ConsumerStatefulWidget {
  const AddRecordPage({super.key, this.editRecord});

  final HealthRecord? editRecord;

  @override
  ConsumerState<AddRecordPage> createState() => _AddRecordPageState();
}

class _AddRecordPageState extends ConsumerState<AddRecordPage> {
  bool get isEditMode => widget.editRecord != null;

  @override
  void initState() {
    super.initState();
    // 如果是编辑模式，在下一帧初始化数据
    if (isEditMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeEditData();
      });
    }
  }

  void _initializeEditData() {
    final record = widget.editRecord;
    if (record != null) {
      final addRecordController = ref.read(
          addRecordControllerProvider.notifier);

      // 设置记录类型
      addRecordController.setRecordType(record.type);

      // 设置输入模式为手动输入
      addRecordController.setInputType(AddRecordInputType.manual);

      // 根据记录类型设置数据
      addRecordController.setInitialData(record);
    }
  }

  @override
  Widget build(BuildContext context) {
    final inputType = ref.watch(addRecordControllerProvider.select((p) => p.inputType));
    final currentRecordType = ref.watch(addRecordControllerProvider.select((p) => p.recordType));
    final healthTypes = ref.read(healthTypeProvider).types;

    // 编辑模式下只显示当前记录类型的tab
    final displayTypes = isEditMode ? [currentRecordType] : healthTypes;

    return DefaultTabController(
      length: displayTypes.length,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          title: Text(isEditMode ? 'edit_record'.tr() : 'add_record'.tr()),
          bottom: RecordTypeTabs(types: displayTypes),
          actions: isEditMode ? [
            IconButton(
              onPressed: _onDelete,
              icon: const Icon(Icons.delete_outline, color: Colors.red),
              tooltip: 'delete_record'.tr(),
            ),
          ] : null,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomLeft,
                colors: context.appColors.backgroundGradient,
                stops: const [0, 0.2, 0.5, 1],
              ),
            ),
          ),
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: isEditMode
              ? _buildEditModeBody(currentRecordType, inputType)
              : TabBarView(
                  children: healthTypes.map((recordType) {
                    final service = HealthServiceManager.getService(recordType);
                    final canScan = service.canScan();
                    return SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: !canScan || inputType == AddRecordInputType.manual
                            ? InputForm(service: service, canScan: canScan, onSave: _onSave)
                            : const ScanWidget(),
                      ),
                    );
                  }).toList(),
                ),
        ),
      ),
     );
  }

  Widget _buildEditModeBody(HealthRecordTypeEnum recordType, AddRecordInputType inputType) {
    final service = HealthServiceManager.getService(recordType);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: InputForm(service: service, canScan: false, onSave: _onSave), // 编辑模式下不显示扫描功能
      ),
    );
  }

  void _onSave() async {
    final success = isEditMode
        ? await ref.read(addRecordControllerProvider.notifier).updateCurrentRecord(widget.editRecord!)
        : await ref.read(addRecordControllerProvider.notifier).saveCurrentRecord();

    if (success && mounted) {
      Navigator.of(context).pop(true); // 返回 true 表示保存成功
    } else if (mounted) {
      // 保存失败，显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('save_failed').tr(),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 删除记录
  void _onDelete() async {
    if (!isEditMode || widget.editRecord == null) {
      return;
    }

    // 显示确认删除对话框
    final confirmed = await ConfirmDeleteDialog.show(
      context: context,
      title: 'delete_record_title'.tr(),
      content: 'delete_record_content'.tr(),
    );

    if (!confirmed || !mounted) {
      return;
    }

    // 执行删除操作
    final success = await ref.read(addRecordControllerProvider.notifier).deleteRecord(widget.editRecord!.id);

    if (success && mounted) {
      Navigator.of(context).pop(true); // 返回 true 表示删除成功，需要刷新数据
    } else if (mounted) {
      // 删除失败，显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('delete_failed').tr(),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
