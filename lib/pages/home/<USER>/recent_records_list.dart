import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:health_diary/pages/add_record/add_record_page.dart';
import 'package:health_diary/repository/health_record_repository.dart';
import 'package:health_diary/utils/time_formatter.dart';

import '../../../themes/app_theme.dart';
import '../../../types/health_record.dart';
import '../controller/recent_records_controller.dart';
import 'data_item_widget.dart';

/// 最近记录列表
class RecentRecordsList extends ConsumerStatefulWidget {
  const RecentRecordsList({super.key});

  @override
  ConsumerState<RecentRecordsList> createState() => _RecentRecordsListState();
}

class _RecentRecordsListState extends ConsumerState<RecentRecordsList> {

  /// 刷新数据
  Future<void> _refreshData() async {
    await ref.read(recentRecordsControllerProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final asyncRecords = ref.watch(recentRecordsControllerProvider);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'recent_records'.tr(),
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            child: _buildAsyncContent(asyncRecords),
          ),
        ),
      ],
    );
  }

  /// 构建异步内容
  Widget _buildAsyncContent(AsyncValue<List<HealthRecordEntry>> asyncRecords) {
    return asyncRecords.when(
      data: (records) {
        if (records.isEmpty) {
          return _buildEmptyState();
        }
        return _buildRecordsList(records);
      },
      loading: () => _buildLoadingIndicator(),
      error: (error, stackTrace) => _buildErrorState(error.toString()),
    );
  }

  /// 构建记录列表
  Widget _buildRecordsList(List<HealthRecordEntry> records) {
    final controller = ref.read(recentRecordsControllerProvider.notifier);
    final hasMore = controller.hasMore;

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: records.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        // 如果是最后一个item且还有更多数据，显示加载指示器并触发加载
        if (index == records.length) {
          // 触发加载更多
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (hasMore && !controller.isLoading) {
              controller.loadMore();
            }
          });
          return null;
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: RecordItem(record: records[index]),
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'no_records_yet'.tr(),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.red[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.read(recentRecordsControllerProvider.notifier).refresh();
            },
            child: Text('retry'.tr()),
          ),
        ],
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

/// 根据记录颜色值获取对应的主题颜色
Color _getRecordCardColor(BuildContext context, int colorValue) {
  switch (colorValue) {
    case 0xFFFFEBF0:
      return context.appColors.bloodPressureCard;
    case 0xFFE4F4FF:
      return context.appColors.bloodSugarCard;
    default:
      return Color(colorValue);
  }
}

/// 记录条目
class RecordItem extends ConsumerStatefulWidget {
  final HealthRecordEntry record;

  const RecordItem({super.key, required this.record});

  @override
  ConsumerState<RecordItem> createState() => _RecordItemState();
}

class _RecordItemState extends ConsumerState<RecordItem> {
  bool _isNoteExpanded = false;

  @override
  Widget build(BuildContext context) {
    final hasNote = widget.record.note.isNotEmpty;

    return GestureDetector(
      onTap: hasNote ? _toggleNoteExpansion : null,
      onLongPress: () => _onLongPressEdit(context, ref),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getRecordCardColor(context, widget.record.color),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：左上角时间，右上角记录类型
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.record.recordType.name.tr(),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  TimeFormatter.formatDateTime(widget.record.time, context.locale),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 数据显示区域
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: widget.record.values.map((value) => DataItemWidget(
                label: value.label,
                value: value.value,
                unit: value.unit,
                isAbnormal: value.isAbnormal,
              )).toList(),
            ),
            // 备注区域
            if (hasNote) ..._buildNoteSection(context),
          ],
        ),
      ),
    );
  }

  /// 构建备注区域
  List<Widget> _buildNoteSection(BuildContext context) {
    return [
      AnimatedCrossFade(
        duration: const Duration(milliseconds: 200),
        crossFadeState: _isNoteExpanded
            ? CrossFadeState.showSecond
            : CrossFadeState.showFirst,
        firstChild: const SizedBox.shrink(),
        secondChild: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            // 分割线
            Container(
              height: 1,
              color: Colors.grey.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 12),
            // 备注内容
            Text(
              widget.record.note,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    ];
  }

  /// 切换备注展开状态
  void _toggleNoteExpansion() {
    setState(() {
      _isNoteExpanded = !_isNoteExpanded;
    });
  }

  /// 长按编辑记录
  Future<void> _onLongPressEdit(BuildContext context, WidgetRef ref) async {
    // 获取原始记录
    final repository = ref.read(healthRecordRepositoryProvider);
    final originalRecord = await repository.getHealthRecordById(widget.record.id);

    if (originalRecord != null && context.mounted) {
      // 打开编辑页面
      await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => AddRecordPage(editRecord: originalRecord),
        ),
      );
    }
  }
}
