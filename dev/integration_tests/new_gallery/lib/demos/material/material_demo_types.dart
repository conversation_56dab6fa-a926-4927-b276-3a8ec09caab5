// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

enum BottomNavigationDemoType { withLabels, withoutLabels }

enum BottomSheetDemoType { persistent, modal }

enum ButtonDemoType { text, elevated, outlined, toggle, floating }

enum ChipDemoType { action, choice, filter, input }

enum DialogDemoType { alert, alertTitle, simple, fullscreen }

enum GridListDemoType { imageOnly, header, footer }

enum ListDemoType { oneLine, twoLine }

enum MenuDemoType { contextMenu, sectionedMenu, simpleMenu, checklistMenu }

enum PickerDemoType { date, time, range }

enum ProgressIndicatorDemoType { circular, linear }

enum SelectionControlsDemoType { checkbox, radio, switches }

enum SlidersDemoType { sliders, rangeSliders, customSliders }

enum TabsDemoType { scrollable, nonScrollable }

enum DividerDemoType { horizontal, vertical }
